from typing import Any, Optional

from app.core.logging import logger
from ..abs.cache_registry import CacheRegistry


class UnifiedCacheService:
    """统一缓存服务 - 管理所有类型的缓存"""

    def __init__(self, cache_registry: CacheRegistry):
        self.cache_registry = cache_registry

    async def refresh_all_caches(self) -> dict[str, Any]:
        """刷新所有类型的缓存"""
        cache_types = self.cache_registry.get_registered_types()
        results = {}

        total_success = 0
        total_failed = 0

        logger.info(f"开始刷新{len(cache_types)}种类型的缓存")

        for cache_type in cache_types:
            try:
                manager = self.cache_registry.get_cache_manager(cache_type)
                result = await manager.refresh_all_cache()
                results[cache_type] = result

                total_success += result.get('success_count', 0)
                total_failed += result.get('failed_count', 0)

                logger.info(f"缓存类型 {cache_type} 刷新完成")

            except Exception as e:
                logger.error(f"刷新缓存类型 {cache_type} 失败: {str(e)}")
                results[cache_type] = {
                    'cache_type': cache_type,
                    'error': str(e),
                    'success_count': 0,
                    'failed_count': 0
                }

        logger.info(f"所有缓存刷新完成，总成功: {total_success}，总失败: {total_failed}")

        return {
            'total_cache_types': len(cache_types),
            'total_success': total_success,
            'total_failed': total_failed,
            'details': results
        }

    async def refresh_cache_by_type(self, cache_type: str, **kwargs) -> dict[str, Any]:
        """刷新指定类型的缓存"""
        try:
            manager = self.cache_registry.get_cache_manager(cache_type, **kwargs)
            result = await manager.refresh_all_cache()

            logger.info(f"缓存类型 {cache_type} 刷新完成")
            return {
                'success': True,
                'cache_type': cache_type,
                'result': result
            }

        except Exception as e:
            logger.error(f"刷新缓存类型 {cache_type} 失败: {str(e)}")
            return {
                'success': False,
                'cache_type': cache_type,
                'error': str(e)
            }

    async def get_cached_data(self, cache_type: str, **kwargs) -> Optional[Any]:
        """获取指定类型的缓存数据"""
        try:
            manager = self.cache_registry.get_cache_manager(cache_type, **kwargs)
            return await manager.get_cached_data(**kwargs)
        except Exception as e:
            logger.error(f"获取缓存数据失败 {cache_type}: {str(e)}")
            return None

    async def invalidate_cache(self, cache_type: str, **kwargs) -> bool:
        """使指定缓存失效"""
        try:
            manager = self.cache_registry.get_cache_manager(cache_type, **kwargs)
            return await manager.invalidate_cache(**kwargs)
        except Exception as e:
            logger.error(f"使缓存失效失败 {cache_type}: {str(e)}")
            return False

    async def get_all_cache_stats(self) -> dict[str, Any]:
        """获取所有缓存统计信息"""
        cache_types = self.cache_registry.get_registered_types()
        stats = {}

        for cache_type in cache_types:
            try:
                manager = self.cache_registry.get_cache_manager(cache_type)
                stats[cache_type] = await manager.get_cache_stats()
            except Exception as e:
                logger.error(f"获取缓存统计失败 {cache_type}: {str(e)}")
                stats[cache_type] = {
                    'cache_type': cache_type,
                    'error': str(e)
                }

        return {
            'total_cache_types': len(cache_types),
            'cache_stats': stats
        }

    def get_registered_cache_types(self) -> list[str]:
        """获取已注册的缓存类型列表"""
        return self.cache_registry.get_registered_types()
