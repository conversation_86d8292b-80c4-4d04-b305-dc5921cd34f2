from typing import List, Optional, Dict, Any

from app.db.repository.static_config_repository import AsyncStaticConfigRepo
from .config_cache_config import StaticConfigData
from ..abs.cache_repository import CacheRepository


class StaticConfigCacheRepository(CacheRepository):
    """静态配置缓存数据源"""

    def __init__(self, static_config_repo: AsyncStaticConfigRepo):
        self.static_config_repo = static_config_repo

    async def get_all_active_data(self) -> List[StaticConfigData]:
        """获取所有活跃的静态配置数据"""
        result = []

        # 获取所有有效的静态配置
        configs = await self.static_config_repo.get_active_configs()

        for config in configs:
            result.append(StaticConfigData(
                cfg_type=config.cfg_type,
                cfg_code=config.cfg_code,
                cfg_name=config.cfg_name,
                cfg_value=config.cfg_value,
                remark=config.remark,
                status=config.status
            ))

        return result

    async def get_data_by_keys(self, cfg_type: str, cfg_code: str, **kwargs) -> Optional[StaticConfigData]:
        """根据配置类型和编码获取数据"""
        config = await self.static_config_repo.get_by_type_and_code(cfg_type, cfg_code)
        if not config:
            return None

        return StaticConfigData(
            cfg_type=config.cfg_type,
            cfg_code=config.cfg_code,
            cfg_name=config.cfg_name,
            cfg_value=config.cfg_value,
            remark=config.remark,
            status=config.status
        )

    async def get_data_count(self) -> int:
        """获取静态配置总数"""
        return await self.static_config_repo.count_active_configs()

    async def get_configs_by_type(self, cfg_type: str) -> List[StaticConfigData]:
        """根据配置类型获取所有配置"""
        configs = await self.static_config_repo.get_by_type(cfg_type)
        result = []

        for config in configs:
            if config.status == "1":  # 只返回有效配置
                result.append(StaticConfigData(
                    cfg_type=config.cfg_type,
                    cfg_code=config.cfg_code,
                    cfg_name=config.cfg_name,
                    cfg_value=config.cfg_value,
                    remark=config.remark,
                    status=config.status
                ))

        return result

    async def get_config_value(self, cfg_type: str, cfg_code: str, default_value: Any = None) -> Any:
        """获取配置值，支持默认值"""
        config = await self.get_data_by_keys(cfg_type, cfg_code)
        if not config or not config.is_active():
            return default_value

        return config.get_parsed_value()

    async def get_config_as_dict(self, cfg_type: str) -> Dict[str, Any]:
        """获取指定类型的所有配置并转换为字典"""
        configs = await self.get_configs_by_type(cfg_type)
        result = {}

        for config in configs:
            result[config.cfg_code] = config.get_parsed_value()

        return result 