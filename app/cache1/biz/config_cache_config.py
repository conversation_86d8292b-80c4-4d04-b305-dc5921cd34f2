import json
from dataclasses import dataclass
from datetime import <PERSON><PERSON><PERSON>
from typing import Any, Optional

from app.core.encrypt import ConfigEncryption
from app.core.logging import logger
from ..abs.cache_config import CacheConfig
from ..enums import CacheBizType


@dataclass
class StaticConfigData:
    """静态配置数据类"""
    cfg_type: str
    cfg_code: str
    cfg_name: str
    cfg_value: str
    remark: Optional[str] = None

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            'cfg_type': self.cfg_type,
            'cfg_code': self.cfg_code,
            'cfg_name': self.cfg_name,
            'cfg_value': self.cfg_value,
            'remark': self.remark or '',
        }

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> 'StaticConfigData':
        """从字典数据创建实例"""
        return cls(
            cfg_type=data.get('cfg_type', ''),
            cfg_code=data.get('cfg_code', ''),
            cfg_name=data.get('cfg_name', ''),
            cfg_value=data.get('cfg_value', ''),
            remark=data.get('remark'),
        )

    def get_parsed_value(self) -> Any:
        """解析配置值，支持JSON格式"""
        if not self.cfg_value:
            return None

        # 尝试解析为JSON
        try:
            return json.loads(self.cfg_value)
        except (json.JSONDecodeError, TypeError):
            # 如果不是JSON格式，返回原始字符串
            return self.cfg_value


@dataclass
class StaticConfigCacheConfig(CacheConfig):
    """静态配置缓存配置"""

    encryption_key: Optional[str] = None

    def __post_init__(self):
        super().__post_init__()
        self.cache_type = CacheBizType.STATIC_CONST.value
        self.cache_key_prefix = "cfg_static_const_cache"
        self.expire_time = timedelta(hours=12)  # 配置数据12小时过期
        self.use_hash_storage = True
        self.enable_encryption = False
        self.table_name = "shj_cfg_static_const"
        self.primary_keys = ['cfg_type', 'cfg_code']

        # 初始化加密器
        if self.encryption_key:
            self.encryptor = ConfigEncryption(self.encryption_key)
            self.enable_encryption = True

    def get_cache_field_name(self, cfg_type: str, cfg_code: str, **kwargs) -> str:
        """生成缓存字段名称"""
        return f"{cfg_type}:{cfg_code}"

    def serialize_data(self, data: Any) -> str:
        """序列化静态配置数据"""
        if isinstance(data, dict):
            return json.dumps(data, ensure_ascii=False)
        elif isinstance(data, StaticConfigData):
            return json.dumps(data.to_dict(), ensure_ascii=False)
        else:
            raise ValueError(f"不支持的序列化数据类型: {type(data)}")

    def deserialize_data(self, data: str) -> StaticConfigData:
        """反序列化静态配置数据"""
        try:
            config_data = json.loads(data)
            model_data = StaticConfigData.from_dict(config_data)
            # 解密可能存在加密的配置值
            model_data.cfg_value = self._decrypt_data(model_data.cfg_value)
            return model_data
        except json.JSONDecodeError as e:
            logger.error(f"反序列化静态配置数据失败: {str(e)}")
            raise

    def extract_cache_key_values(self, data: Any) -> dict[str, Any]:
        """从数据中提取缓存键值"""
        if isinstance(data, StaticConfigData):
            return {
                'cfg_type': data.cfg_type,
                'cfg_code': data.cfg_code
            }
        else:
            raise ValueError(f"不支持的数据类型: {type(data)}")

    def get_type_cache_key(self, cfg_type: str) -> str:
        """获取按类型缓存的键"""
        return f"{self.get_full_cache_key()}:type:{cfg_type}"

    def _decrypt_data(self, encrypted_value: str) -> str:
        """解密加密的配置值"""
        if not self.enable_encryption or not hasattr(self, 'encryptor'):
            return encrypted_value

        try:
            if encrypted_value.startswith('ENC(') and encrypted_value.endswith(')'):
                encrypted_content = encrypted_value[4:-1]
                return self.encryptor.decrypt(encrypted_content)
            else:
                return encrypted_value
        except Exception as e:
            logger.error(f"解密API密钥失败: {str(e)}")
            raise ValueError(f"API密钥解密失败: {str(e)}")
