from typing import List, Any, Optional

from app.db import Async<PERSON><PERSON>rovidersRepo, AsyncLLMModelsRepo, AIModelType
from .llm_cache_config import LLMModelConfig
from ..abs.cache_repository import CacheRepository


class LLMModelCacheRepository(CacheRepository):
    """LLM模型缓存数据源"""

    def __init__(
        self,
        llm_providers_repo: AsyncLLMProvidersRepo,
        llm_models_repo: AsyncLLMModelsRepo
    ):
        self.llm_providers_repo = llm_providers_repo
        self.llm_models_repo = llm_models_repo

    async def get_all_active_data(self) -> List[LLMModelConfig]:
        """获取所有活跃的LLM模型数据"""
        result = []

        # 获取所有有效供应商
        providers = await self.llm_providers_repo.get_active_providers()

        for provider in providers:
            # 获取该供应商下的所有LLM模型
            models = await self.llm_models_repo.get_models_by_provider(
                provider_id=provider.id,
                model_type=AIModelType.LLM
            )

            # 组合provider和model数据
            for model in models:
                LLMModelConfig(
                    provider_name=provider.provider_name,
                    model_name=model.model_name,
                    api_base_url=model.api_endpoint or provider.api_base_url,
                    api_key=model.api_key or provider.api_key,
                    max_output_tokens=model.max_output_tokens,
                    default_headers=provider.default_headers,
                    custom_config=model.custom_config
                )

                # 创建包含provider和model信息的组合对象
                combined_data = type('CombinedData', (), {
                    'provider': provider,
                    'model': model,
                    # 为了兼容，直接暴露model的属性
                    'provider_name': provider.provider_name,
                    'model_name': model.model_name,
                    'api_endpoint': model.api_endpoint,
                    'api_key': model.api_key,
                    'max_output_tokens': model.max_output_tokens,
                    'custom_config': model.custom_config
                })()
                result.append(combined_data)

        return result

    async def get_data_by_keys(self, provider_name: str, model_name: str, **kwargs) -> Optional[Any]:
        """根据供应商和模型名称获取数据"""
        # 获取供应商
        provider = await self.llm_providers_repo.get_by_provider_name(provider_name)
        if not provider:
            return None

        # 获取模型
        models = await self.llm_models_repo.get_models_by_provider(
            provider_id=provider.id,
            model_type=AIModelType.LLM
        )

        model = next((m for m in models if m.model_name == model_name), None)
        if not model:
            return None

        # 返回组合数据
        return type('CombinedData', (), {
            'provider': provider,
            'model': model,
            'provider_name': provider.provider_name,
            'model_name': model.model_name,
            'api_endpoint': model.api_endpoint,
            'api_key': model.api_key,
            'max_output_tokens': model.max_output_tokens,
            'custom_config': model.custom_config
        })()

    async def get_data_count(self) -> int:
        """获取LLM模型总数"""
        providers = await self.llm_providers_repo.get_active_providers()
        total_count = 0

        for provider in providers:
            models = await self.llm_models_repo.get_models_by_provider(
                provider_id=provider.id,
                model_type=AIModelType.LLM
            )
            total_count += len(models)

        return total_count
