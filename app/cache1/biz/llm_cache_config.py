import json
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from typing import Any, Optional

from app.core.encrypt.base import ConfigEncryption
from app.core.logging import logger
from ..abs.cache_config import CacheConfig
from ..enums import CacheBizType


@dataclass
class LLMModelConfig:
    """LLM模型配置数据类"""
    provider_name: str
    model_name: str
    api_base_url: str
    api_key: Optional[str]
    max_output_tokens: Optional[int] = None
    default_headers: Optional[dict[str, Any]] = None
    custom_config: Optional[dict[str, Any]] = None

    def to_dict(self) -> dict[str, Any]:
        """转换为字典格式"""
        return {
            'provider_name': self.provider_name,
            'model_name': self.model_name,
            'api_base_url': self.api_base_url,
            'api_key': self.api_key or '',
            'max_output_tokens': self.max_output_tokens,
            'default_headers': self.default_headers or {},
            'custom_config': self.custom_config or {}
        }

    @classmethod
    def from_dict(cls, data: dict[str, Any]) -> 'LLMModelConfig':
        """从字典数据创建实例"""
        return cls(
            provider_name=data.get('provider_name', ''),
            model_name=data.get('model_name', ''),
            api_base_url=data.get('api_base_url', ''),
            api_key=data.get('api_key') if data.get('api_key') else None,
            max_output_tokens=data.get('max_output_tokens'),
            default_headers=data.get('default_headers'),
            custom_config=data.get('custom_config')
        )


@dataclass
class LLMModelCacheConfig(CacheConfig):
    """LLM模型缓存配置"""

    encryption_key: Optional[str] = None

    def __post_init__(self):
        super().__post_init__()
        self.cache_type = CacheBizType.LLM_MODEL.value
        self.cache_key_prefix = "llm_provider_model_cache"
        self.expire_time = timedelta(hours=24)
        self.use_hash_storage = True
        self.enable_encryption = True

        # 初始化加密器
        if self.encryption_key:
            self.encryptor = ConfigEncryption(self.encryption_key)

    def get_cache_field_name(self, provider_name: str, model_name: str, **kwargs) -> str:
        """生成缓存字段名称"""
        return f"{provider_name}:{model_name}"

    def serialize_data(self, data: Any) -> str:
        """序列化LLM模型配置数据"""
        if isinstance(data, dict):
            return json.dumps(data, ensure_ascii=False)
        elif isinstance(data, LLMModelConfig):
            return json.dumps(data.to_dict(), ensure_ascii=False)
        else:
            raise ValueError(f"不支持的序列化数据类型: {type(data)}")

    def deserialize_data(self, data: str) -> LLMModelConfig:
        """反序列化LLM模型配置数据"""
        try:
            config_data = json.loads(data)

            # 解密API密钥
            if config_data.get('api_key') and self.enable_encryption:
                config_data['api_key'] = self._decrypt_api_key(config_data['api_key'])

            return LLMModelConfig.from_dict(config_data)
        except json.JSONDecodeError as e:
            logger.error(f"反序列化LLM配置数据失败: {str(e)}")
            raise

    def extract_cache_key_values(self, data: Any) -> dict[str, Any]:
        """从数据中提取缓存键值"""
        if isinstance(data, LLMModelConfig):
            return {
                'provider_name': data.provider_name,
                'model_name': data.model_name
            }
        else:
            raise ValueError(f"不支持的数据类型: {type(data)}")

    def _decrypt_api_key(self, encrypted_api_key: str) -> str:
        """解密API密钥"""
        if not self.enable_encryption or not hasattr(self, 'encryptor'):
            return encrypted_api_key

        try:
            if encrypted_api_key.startswith('ENC(') and encrypted_api_key.endswith(')'):
                encrypted_content = encrypted_api_key[4:-1]
                return self.encryptor.decrypt(encrypted_content)
            else:
                logger.warning("API密钥未加密存储")
                return encrypted_api_key
        except Exception as e:
            logger.error(f"解密API密钥失败: {str(e)}")
            raise ValueError(f"API密钥解密失败: {str(e)}")
